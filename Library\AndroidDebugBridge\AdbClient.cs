#define TRACE

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library.AndroidDebugBridge;

public sealed class AdbClient : IDisposable
{
    private const string ADB_EXECUTABLE_PATH = "Data\\adb.exe";
    private const string ADB_RESOURCE_PATH = "SamsungTool.Resources.adb.exe";
    private const string ADB_EXECUTABLE_HASH = "B40ABDA76F72462483A95321CAF431B752B7988DE0F92BCB0BA27BF6E3B86BFC";

    public string ServerHost { get; private set; }
    public int ServerPort { get; private set; }
    public string? DeviceSerialNumber { get; private set; }
    private bool _disposed = false;

    public AdbClient(int adbServerPort = -1, string? adbServerHost = null)
    {
        if (adbServerPort < 0)
        {
            string? environmentVariable = Environment.GetEnvironmentVariable("ANDROID_ADB_SERVER_PORT");
            int result = 0;
            ServerPort = ((string.IsNullOrEmpty(environmentVariable) || !int.TryParse(environmentVariable, out result)) ? 5037 : result);
        }
        else
        {
            ServerPort = adbServerPort;
        }
        ServerHost = (string.IsNullOrEmpty(adbServerHost) ? "127.0.0.1" : adbServerHost);

        // Clean up old temporary files
        try
        {
            CleanupTempFiles("retails_*.apk");
            CleanupTempFiles("retails_OldSDK_*.apk");
            CleanupTempFiles("fota_*.apk");
        }
        catch
        {
            // Ignore cleanup errors during initialization
        }
    }

    private bool VerifyAndEnsureAdbExecutable()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string adbPath = Path.Combine(Directory.GetCurrentDirectory(), ADB_EXECUTABLE_PATH);
        const int maxRetries = 3;
        const int retryDelayMs = 1000;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                string? dataDir = Path.GetDirectoryName(adbPath);
                if (!string.IsNullOrEmpty(dataDir) && !Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }

                bool isValid = false;

                // Check if file exists and is valid
                if (File.Exists(adbPath))
                {
                    try
                    {
                        string fileHash = ComputeFileHash(adbPath);
                        isValid = string.Equals(fileHash, ADB_EXECUTABLE_HASH, StringComparison.OrdinalIgnoreCase);

                        if (!isValid)
                        {
                            File.Delete(adbPath);
                        }
                    }
                    catch
                    {
                        // If hash computation fails, delete and re-extract
                        try { File.Delete(adbPath); } catch { }
                        isValid = false;
                    }
                }

                if (!isValid)
                {
                    // Try to extract resource with retry logic
                    bool extractionSuccess = false;
                    
                    for (int extractAttempt = 0; extractAttempt < 2; extractAttempt++)
                    {
                        try
                        {
                            ExtractResourceToFile(ADB_RESOURCE_PATH, adbPath);
                            
                            // Verify extraction
                            if (File.Exists(adbPath) && new FileInfo(adbPath).Length > 0)
                            {
                                extractionSuccess = true;
                                break;
                            }
                        }
                        catch
                        {
                            if (extractAttempt == 0)
                            {
                                Thread.Sleep(500);
                            }
                        }
                    }

                    if (!extractionSuccess)
                    {
                        if (attempt < maxRetries - 1)
                        {
                            Thread.Sleep(retryDelayMs);
                            continue;
                        }
                        return false;
                    }

                    // Verify hash after extraction
                    try
                    {
                        string newHash = ComputeFileHash(adbPath);
                        isValid = string.Equals(newHash, ADB_EXECUTABLE_HASH, StringComparison.OrdinalIgnoreCase);

                        if (!isValid)
                        {
                            if (attempt < maxRetries - 1)
                            {
                                Thread.Sleep(retryDelayMs);
                                continue;
                            }
                            return false;
                        }
                    }
                    catch
                    {
                        // If hash verification fails, but file exists, proceed (may be AOT issue)
                        if (File.Exists(adbPath) && new FileInfo(adbPath).Length > 0)
                        {
                            isValid = true;
                        }
                    }
                }

                return isValid;
            }
            catch (Exception)
            {
                if (attempt < maxRetries - 1)
                {
                    Thread.Sleep(retryDelayMs);
                    continue;
                }
                return false;
            }
        }

        return false;
    }

    private string ComputeFileHash(string filePath)
    {
        using (var sha256 = System.Security.Cryptography.SHA256.Create())
        using (var stream = File.OpenRead(filePath))
        {
            byte[] hashBytes = sha256.ComputeHash(stream);
            return BitConverter.ToString(hashBytes).Replace("-", "");
        }
    }

    private void ExtractResourceToFile(string resourceName, string outputPath)
    {
        var assembly = System.Reflection.Assembly.GetExecutingAssembly();

        Stream? resourceStream = null;
        try
        {
            // Try multiple resource name patterns for AOT compatibility
            string[] possibleResourceNames = {
                resourceName,
                resourceName.Replace("SamsungTool.Resources.", ""),
                resourceName.Replace("SamsungTool.Resources.", "Resources."),
                Path.GetFileName(resourceName)
            };

            foreach (string possibleName in possibleResourceNames)
            {
                try
                {
                    resourceStream = assembly.GetManifestResourceStream(possibleName);
                    if (resourceStream != null) break;
                }
                catch { }
            }

            // If direct resource access fails, try finding by name pattern
            if (resourceStream == null)
            {
                string[] resources = assembly.GetManifestResourceNames();
                string targetFileName = Path.GetFileName(resourceName);
                
                // Try exact match first
                string? actualName = resources.FirstOrDefault(r => r.EndsWith(targetFileName, StringComparison.OrdinalIgnoreCase));
                
                // If not found, try partial match
                if (string.IsNullOrEmpty(actualName))
                {
                    actualName = resources.FirstOrDefault(r => r.Contains(targetFileName.Replace(".exe", ""), StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(actualName))
                {
                    resourceStream = assembly.GetManifestResourceStream(actualName);
                }
            }

            if (resourceStream == null)
            {
                throw new FileNotFoundException($"Resource not found: {resourceName}. Available resources: {string.Join(", ", assembly.GetManifestResourceNames())}");
            }

            // Ensure output directory exists
            string? outputDir = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            // Extract resource to file with proper buffering
            using (FileStream fileStream = File.Create(outputPath))
            {
                resourceStream.CopyTo(fileStream);
                fileStream.Flush();
            }

            // Verify extraction success
            if (!File.Exists(outputPath) || new FileInfo(outputPath).Length == 0)
            {
                throw new InvalidOperationException($"Failed to extract resource to {outputPath}");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to extract resource {resourceName}: {ex.Message}", ex);
        }
        finally
        {
            resourceStream?.Dispose();
        }
    }

    public bool StartServer()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        const int maxRetries = 3;
        const int retryDelayMs = 2000;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                if (!VerifyAndEnsureAdbExecutable())
                {
                    if (attempt < maxRetries - 1)
                    {
                        Thread.Sleep(retryDelayMs);
                        continue;
                    }
                    return false;
                }

                using (Process process = new Process())
                {
                    ProcessStartInfo startInfo = process.StartInfo;
                    startInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), ADB_EXECUTABLE_PATH);
                    startInfo.Arguments = "start-server";
                    startInfo.UseShellExecute = false;
                    startInfo.CreateNoWindow = true;
                    startInfo.RedirectStandardInput = true;
                    startInfo.RedirectStandardOutput = true;
                    startInfo.RedirectStandardError = true;
                    startInfo.StandardOutputEncoding = Encoding.ASCII;
                    startInfo.StandardErrorEncoding = Encoding.ASCII;

                    process.Start();
                    ProcessManager.RegisterProcess(process);

                    // Read output with timeout
                    bool finished = process.WaitForExit(10000); // 10 second timeout
                    
                    if (!finished)
                    {
                        try { process.Kill(true); } catch { }
                        if (attempt < maxRetries - 1)
                        {
                            Thread.Sleep(retryDelayMs);
                            continue;
                        }
                        return false;
                    }

                    // Read any remaining output
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();

                    // Check if there were any critical errors
                    if (!string.IsNullOrEmpty(error) && 
                        !error.Contains("daemon") && 
                        !error.Contains("already running") &&
                        error.Contains("error"))
                    {
                        if (attempt < maxRetries - 1)
                        {
                            Thread.Sleep(retryDelayMs);
                            continue;
                        }
                        return false;
                    }

                    return true;
                }
            }
            catch (Exception)
            {
                if (attempt < maxRetries - 1)
                {
                    Thread.Sleep(retryDelayMs);
                    continue;
                }
                return false;
            }
        }

        return false;
    }

    public async Task<string?> ADBConsole(string cmd, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string pathadb = Path.Combine(Directory.GetCurrentDirectory(), ADB_EXECUTABLE_PATH);

        if (!VerifyAndEnsureAdbExecutable())
        {
            return null;
        }

        if (!File.Exists(pathadb))
        {
            return null;
        }

        const int maxRetries = 2;
        const int timeoutMs = 45000; // Increased timeout for slow operations

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            Process? adbProcess = null;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = pathadb,
                    Arguments = cmd,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    StandardOutputEncoding = Encoding.UTF8,
                    StandardErrorEncoding = Encoding.UTF8
                };

                adbProcess = new Process { StartInfo = startInfo };
                
                // Add delay before starting process for stability
                if (attempt > 0)
                {
                    await Task.Delay(1000, cancellationToken);
                }

                adbProcess.Start();
                ProcessManager.RegisterProcess(adbProcess);

                // Use StringBuilder for better performance
                var outputBuilder = new StringBuilder();
                var errorBuilder = new StringBuilder();

                // Handle output asynchronously
                var outputTask = ReadStreamAsync(adbProcess.StandardOutput, outputBuilder, cancellationToken);
                var errorTask = ReadStreamAsync(adbProcess.StandardError, errorBuilder, cancellationToken);

                // Wait for process with timeout
                var processTask = Task.Run(async () =>
                {
                    using (var timeoutCts = new CancellationTokenSource(timeoutMs))
                    using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                    {
                        try
                        {
                            await Task.Run(() => adbProcess.WaitForExit(), combinedCts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            if (!adbProcess.HasExited)
                            {
                                try { adbProcess.Kill(true); } catch { }
                            }
                            throw;
                        }
                    }
                }, cancellationToken);

                // Wait for all tasks to complete
                await Task.WhenAll(outputTask, errorTask, processTask);

                string output = outputBuilder.ToString();
                string error = errorBuilder.ToString();

                if (!string.IsNullOrEmpty(error) && !error.Contains("daemon") && !error.Contains("* daemon not running"))
                {
                    // If it's a genuine error and not just daemon messages, retry once
                    if (attempt < maxRetries - 1)
                    {
                        continue;
                    }
                    return null;
                }

                return output;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception)
            {
                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(1500, cancellationToken);
                    continue;
                }
                return null;
            }
            finally
            {
                if (adbProcess != null && !adbProcess.HasExited)
                {
                    try { adbProcess.Kill(true); } catch { }
                }

                adbProcess?.Dispose();
            }
        }

        return null;
    }

    private async Task ReadStreamAsync(StreamReader reader, StringBuilder builder, CancellationToken cancellationToken)
    {
        try
        {
            string? line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                cancellationToken.ThrowIfCancellationRequested();
                builder.AppendLine(line);
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception)
        {
            // Ignore stream reading errors
        }
    }

    public void StopServer()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        adbSocket.SendCommand("host:kill");
    }

    public int GetServerVersion()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        adbSocket.SendCommand("host:version");
        return adbSocket.ReadInt32Hex();
    }

    public async Task<bool> InstallRetailsApk(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        const int maxRetries = 3;
        const int retryDelayMs = 2000;

        // Try regular retails.apk first
        bool regularApkSuccess = await TryInstallApk("retails.apk", new string[] {
            "SamsungTool.Resources.retails.apk",
            "retails.apk",
            "Resources.retails.apk"
        }, maxRetries, retryDelayMs, cancellationToken);

        if (regularApkSuccess)
        {
            return true;
        }

        // If regular APK failed, try OldSDK version
        return await TryInstallApk("retails_OldSDK.apk", new string[] {
            "SamsungTool.Resources.retails_OldSDK.apk",
            "retails_OldSDK.apk",
            "Resources.retails_OldSDK.apk"
        }, maxRetries, retryDelayMs, cancellationToken);
    }

    public async Task<bool> InstallRetailsOldSDKApk(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        const int maxRetries = 3;
        const int retryDelayMs = 2000;

        return await TryInstallApk("retails_OldSDK.apk", new string[] {
            "SamsungTool.Resources.retails_OldSDK.apk",
            "retails_OldSDK.apk",
            "Resources.retails_OldSDK.apk"
        }, maxRetries, retryDelayMs, cancellationToken);
    }

    private async Task<bool> TryInstallApk(string apkName, string[] possibleResourceNames, int maxRetries, int retryDelayMs, CancellationToken cancellationToken)
    {
        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                string tempFile = Path.Combine(Path.GetTempPath(), $"{Path.GetFileNameWithoutExtension(apkName)}_{Guid.NewGuid():N}.apk");

                // Ensure temp directory exists
                string? tempDir = Path.GetDirectoryName(tempFile);
                if (!string.IsNullOrEmpty(tempDir) && !Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // Try different resource names for AOT compatibility
                Stream? resourceStream = null;

                foreach (string resourceName in possibleResourceNames)
                {
                    try
                    {
                        resourceStream = assembly.GetManifestResourceStream(resourceName);
                        if (resourceStream != null) break;
                    }
                    catch { }
                }

                // If direct resource access fails, try finding by name
                if (resourceStream == null)
                {
                    string[] resources = assembly.GetManifestResourceNames();
                    string? actualName = resources.FirstOrDefault(r => r.EndsWith(apkName, StringComparison.OrdinalIgnoreCase));
                    
                    if (!string.IsNullOrEmpty(actualName))
                    {
                        resourceStream = assembly.GetManifestResourceStream(actualName);
                    }
                }

                if (resourceStream == null)
                {
                    if (attempt < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs, cancellationToken);
                        continue;
                    }
                    return false;
                }

                // Extract resource to temp file
                using (resourceStream)
                using (var fileStream = File.Create(tempFile))
                {
                    await resourceStream.CopyToAsync(fileStream, cancellationToken);
                    await fileStream.FlushAsync(cancellationToken);
                }

                // Verify file was created successfully
                if (!File.Exists(tempFile) || new FileInfo(tempFile).Length == 0)
                {
                    try { File.Delete(tempFile); } catch { }
                    if (attempt < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs, cancellationToken);
                        continue;
                    }
                    return false;
                }

                // Add delay before installation
                await Task.Delay(500, cancellationToken);

                // Install APK with retry logic
                string? output = null;
                bool installSuccess = false;
                
                for (int installAttempt = 0; installAttempt < 2; installAttempt++)
                {
                    try
                    {
                        output = await ADBConsole($"install -r \"{tempFile}\"", cancellationToken);
                        if (!string.IsNullOrEmpty(output))
                        {
                            if (output.Contains("Success"))
                            {
                                installSuccess = true;
                                break;
                            }
                            
                            // Check for specific errors that indicate we should try OldSDK version
                            if (apkName.Contains("retails.apk") && !apkName.Contains("OldSDK") &&
                                (output.Contains("INSTALL_FAILED_OLDER_SDK") || 
                                 output.Contains("INSTALL_FAILED_VERSION_DOWNGRADE") ||
                                 output.Contains("requires a higher minimum SDK")))
                            {
                                // Clean up temp file and return false to trigger OldSDK fallback
                                try { File.Delete(tempFile); } catch { }
                                return false;
                            }
                        }
                    }
                    catch
                    {
                        if (installAttempt == 0)
                        {
                            await Task.Delay(1000, cancellationToken);
                        }
                    }
                }

                // Clean up temp file
                try { File.Delete(tempFile); } catch { }

                if (installSuccess)
                {
                    return true;
                }

                // If failed but not last attempt, wait and retry
                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(retryDelayMs, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception)
            {
                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(retryDelayMs, cancellationToken);
                    continue;
                }
                return false;
            }
        }

        return false;
    }

    public async Task<bool> InstallFotaApk(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        const int maxRetries = 3;
        const int retryDelayMs = 2000;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            string tempFile = Path.Combine(Path.GetTempPath(), $"fota_{Guid.NewGuid():N}.apk");

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var assembly = System.Reflection.Assembly.GetExecutingAssembly();

                // Ensure temp directory exists
                string? tempDir = Path.GetDirectoryName(tempFile);
                if (!string.IsNullOrEmpty(tempDir) && !Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // Try different resource names for AOT compatibility
                Stream? resourceStream = null;
                string[] possibleResourceNames = {
                    "SamsungTool.Resources.Fota.apk",
                    "Fota.apk",
                    "Resources.Fota.apk"
                };

                foreach (string resourceName in possibleResourceNames)
                {
                    try
                    {
                        resourceStream = assembly.GetManifestResourceStream(resourceName);
                        if (resourceStream != null) break;
                    }
                    catch { }
                }

                // If direct resource access fails, try finding by name
                if (resourceStream == null)
                {
                    string[] resources = assembly.GetManifestResourceNames();
                    string? actualName = resources.FirstOrDefault(r => r.EndsWith("Fota.apk", StringComparison.OrdinalIgnoreCase));

                    if (!string.IsNullOrEmpty(actualName))
                    {
                        resourceStream = assembly.GetManifestResourceStream(actualName);
                    }
                }

                if (resourceStream == null)
                {
                    if (attempt < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs, cancellationToken);
                        continue;
                    }
                    return false;
                }

                // Extract resource to temp file
                using (resourceStream)
                using (var fileStream = File.Create(tempFile))
                {
                    await resourceStream.CopyToAsync(fileStream, cancellationToken);
                    await fileStream.FlushAsync(cancellationToken);
                }

                // Verify file was created successfully
                if (!File.Exists(tempFile) || new FileInfo(tempFile).Length == 0)
                {
                    try { File.Delete(tempFile); } catch { }
                    if (attempt < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs, cancellationToken);
                        continue;
                    }
                    return false;
                }

                // Add delay before installation
                await Task.Delay(500, cancellationToken);

                // Install APK with retry logic
                string? output = null;
                for (int installAttempt = 0; installAttempt < 2; installAttempt++)
                {
                    try
                    {
                        output = await ADBConsole($"install -r \"{tempFile}\"", cancellationToken);
                        if (!string.IsNullOrEmpty(output) && output.Contains("Success"))
                        {
                            break;
                        }
                    }
                    catch
                    {
                        if (installAttempt == 0)
                        {
                            await Task.Delay(1000, cancellationToken);
                        }
                    }
                }

                // Clean up temp file
                try { File.Delete(tempFile); } catch { }

                if (!string.IsNullOrEmpty(output) && output.Contains("Success"))
                {
                    return true;
                }

                // If failed but not last attempt, wait and retry
                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(retryDelayMs, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                try { File.Delete(tempFile); } catch { }
                throw;
            }
            catch (Exception)
            {
                try { File.Delete(tempFile); } catch { }
                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(retryDelayMs, cancellationToken);
                    continue;
                }
                return false;
            }
        }

        return false;
    }

    public AdbDevice[] GetDevices()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string text = "";
        using (AdbSocket adbSocket = GetSocket())
        {
            adbSocket.SendCommand("host:devices-l");
            text = adbSocket.ReadHexString();
        }
        string[] array = text.Split(new char[2] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        List<AdbDevice> list = new List<AdbDevice>(array.Length);
        string[] array2 = array;
        foreach (string text2 in array2)
        {
            string[] array3 = text2.Split(new char[1] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            string product = "";
            string model = "";
            string device = "";
            for (int j = 2; j < array3.Length; j++)
            {
                string[] array4 = array3[j].Split(new char[1] { ':' }, StringSplitOptions.RemoveEmptyEntries);
                if (2 == array4.Length)
                {
                    switch (array4[0])
                    {
                        case "model":
                            model = array4[1];
                            break;

                        case "device":
                            device = array4[1];
                            break;

                        case "product":
                            product = array4[1];
                            break;
                    }
                }
            }
            list.Add(new AdbDevice(array3[0], product, model, device));
        }
        return list.ToArray();
    }

    public void InstallFromBytes(byte[] data, bool installOnSdCard)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string text = (installOnSdCard ? "/sdcard/tmp/in.apk" : "/storage/emulated/0/in.apk");
        string text2 = (installOnSdCard ? "-s" : "");
        if (GetFileInfo(text).IsFile)
        {
            DeleteFile(text);
        }
        UploadData(data, text);
        ExecuteRemoteCommand("pm install " + text2 + " " + text);
        DeleteFile(text);
    }

    public void UploadData(byte[] data, string remoteFileName, int remoteFilePermissions = 1638)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "SEND", $"{remoteFileName},{remoteFilePermissions}", readResponse: false);
        int num = data.Length;
        using (MemoryStream memoryStream = new MemoryStream(data))
        {
            byte[] array = new byte[65536];
            while (num > 0)
            {
                int num2 = ((num < array.Length) ? num : array.Length);
                int bytesRead = memoryStream.Read(array, 0, num2);
                if (bytesRead != num2)
                {
                    throw new IOException("Failed to read expected number of bytes from memory stream");
                }
                adbSocket.WriteString("DATA");
                adbSocket.WriteInt32(num2);
                adbSocket.Write(array, num2);
                num -= num2;
            }
        }
        adbSocket.WriteString("DONE");
        adbSocket.WriteInt32(AdbHelpers.ToUnixTime(DateTime.Now));
        text = adbSocket.ReadString(4);
        if (!string.Equals(text, "OKAY"))
        {
            throw new AdbInvalidResponseException(text ?? "Unknown error");
        }
    }

    public void PushCustomFile(string localFileName, string remoteFileName, int FilePermissions)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        AdbFileInfo fileInfo = GetFileInfo(remoteFileName);
        if (fileInfo.IsDirectory || fileInfo.IsSymbolicLink)
        {
            remoteFileName = AdbHelpers.CombinePath(remoteFileName, Path.GetFileName(localFileName));
        }
        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "SEND", $"{remoteFileName},{FilePermissions}", readResponse: false);
        FileInfo fileInfo2 = new FileInfo(localFileName);
        int num = (int)fileInfo2.Length;
        using (FileStream fileStream = File.OpenRead(localFileName))
        {
            byte[] array = new byte[65536];
            while (num > 0)
            {
                int num2 = ((num < array.Length) ? num : array.Length);
                int bytesRead = fileStream.Read(array, 0, num2);
                if (bytesRead != num2)
                {
                    throw new IOException("Failed to read expected number of bytes from file");
                }
                adbSocket.WriteString("DATA");
                adbSocket.WriteInt32(num2);
                adbSocket.Write(array, num2);
                num -= num2;
            }
        }
        adbSocket.WriteString("DONE");
        adbSocket.WriteInt32(AdbHelpers.ToUnixTime(fileInfo2.LastWriteTime));
        text = adbSocket.ReadString(4);
        if (!string.Equals(text, "OKAY"))
        {
            throw new AdbInvalidResponseException(text ?? "Unknown error");
        }
    }

    public void PushCustomData(byte[] data, string remoteFileName, int filepermition)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "SEND", $"{remoteFileName},{filepermition}", readResponse: false);
        int num = data.Length;
        using (MemoryStream memoryStream = new MemoryStream(data))
        {
            byte[] array = new byte[65536];
            while (num > 0)
            {
                int num2 = ((num < array.Length) ? num : array.Length);
                int bytesRead = memoryStream.Read(array, 0, num2);
                if (bytesRead != num2)
                {
                    throw new IOException("Failed to read expected number of bytes from memory stream");
                }
                adbSocket.WriteString("DATA");
                adbSocket.WriteInt32(num2);
                adbSocket.Write(array, num2);
                num -= num2;
            }
        }
        FileInfo fileInfo = new FileInfo("Qualcom Flash.exe");
        adbSocket.WriteString("DONE");
        adbSocket.WriteInt32(AdbHelpers.ToUnixTime(fileInfo.LastWriteTime));
        text = adbSocket.ReadString(4);
        if (!string.Equals(text, "OKAY"))
        {
            throw new AdbInvalidResponseException(text ?? "Unknown error");
        }
    }

    public void SetDevice(string serialNumber)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        DeviceSerialNumber = serialNumber;
    }

    public Dictionary<string, string> GetDeviceProperties()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        Dictionary<string, string> dictionary = new Dictionary<string, string>();
        string[] array = ExecuteRemoteCommand("/system/bin/getprop");
        string[] array2 = array;
        foreach (string text in array2)
        {
            Match match = Regex.Match(text, "\\[(.*)]: \\[(.*)]");
            if (match.Success && match.Groups.Count == 3)
            {
                dictionary.Add(match.Groups[1].Value, match.Groups[2].Value);
            }
            else
            {
            }
        }
        return dictionary;
    }

    public string[] ExecuteRemoteCommand(string command)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        SetDevice(adbSocket);
        adbSocket.SendCommand("shell:" + command);
        string[] array = adbSocket.ReadAllLines();
        return array;
    }

    public AdbFileInfo GetFileInfo(string fileName)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "STAT", fileName);
        if (!string.Equals(text, "STAT"))
        {
            throw new AdbInvalidResponseException(text ?? "Unknown response");
        }
        return GetFileInfo(adbSocket, fileName, null) ?? throw new AdbInvalidResponseException("Failed to get file info");
    }

    public AdbFileInfo[] GetDirectoryListing(string directoryName)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "LIST", directoryName);
        List<AdbFileInfo> list = new List<AdbFileInfo>();
        bool flag = false;
        while (!string.Equals(text, "DONE"))
        {
            if (string.Equals(text, "DENT"))
            {
                AdbFileInfo? fileInfo = GetFileInfo(adbSocket, null, directoryName);
                if (fileInfo == null)
                {
                    flag = true;
                }
                else
                {
                    list.Add(fileInfo);
                }
                text = adbSocket.ReadString(4);
                continue;
            }
            throw new AdbInvalidResponseException(text ?? "Unknown response");
        }
        if (!flag)
        {
            throw new DirectoryNotFoundException();
        }
        return list.ToArray();
    }

    private AdbFileInfo? GetFileInfo(AdbSocket adbSocket, string? fullName, string? directoryName)
    {
        int mode = adbSocket.ReadInt32();
        int size = adbSocket.ReadInt32();
        DateTime modified = AdbHelpers.FromUnixTime(adbSocket.ReadInt32());
        string text = "";
        if (!string.IsNullOrEmpty(fullName))
        {
            text = Path.GetFileName(fullName) ?? "";
        }
        else
        {
            text = adbSocket.ReadSyncString();
            if (text.Equals(".") || text.Equals(".."))
            {
                return null;
            }
            fullName = AdbHelpers.CombinePath(directoryName ?? "", text);
        }
        return new AdbFileInfo(fullName ?? "", text, size, mode, modified);
    }

    public void DownloadFile(string remoteFileName, string localFileName)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "RECV", remoteFileName);
        int num = 0;
        using FileStream fileStream = File.Open(localFileName, FileMode.Create);
        byte[] array = new byte[65536];
        while (true)
        {
            if (!string.Equals(text, "DONE"))
            {
                if (!string.Equals(text, "DATA"))
                {
                    break;
                }
                int num2 = adbSocket.ReadInt32();
                adbSocket.Read(array, num2);
                fileStream.Write(array, 0, num2);
                num += num2;
                text = adbSocket.ReadString(4);
                continue;
            }
            return;
        }
        throw new AdbInvalidResponseException(text ?? "Unknown response");
    }

    public async Task<Dictionary<string, string[]>> ExecuteBatchCommands(string[] commands, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        Dictionary<string, string[]> results = new Dictionary<string, string[]>();

        if (commands == null || commands.Length == 0)
        {
            return results;
        }

        var commandGroups = GroupSimilarCommands(commands);

        using (AdbSocket adbSocket = GetSocket())
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                SetDevice(adbSocket);

                foreach (var group in commandGroups)
                {
                    foreach (string command in group.Value)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        try
                        {
                            adbSocket.SendCommand("shell:" + command);
                            string[] response = adbSocket.ReadAllLines();
                            results[command] = response;

                            await Task.Delay(1, cancellationToken);
                        }
                        catch (OperationCanceledException)
                        {
                            throw;
                        }
                        catch (Exception ex)
                        {
                            results[command] = new string[] { $"Error: {ex.Message}" };
                        }
                    }

                    await Task.Delay(50, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                results["_cancelled"] = new string[] { "Batch commands cancelled" };
                throw;
            }
            catch (Exception ex)
            {
                results["_error"] = new string[] { $"Error: {ex.Message}" };
            }
        }

        return results;
    }

    private Dictionary<string, List<string>> GroupSimilarCommands(string[] commands)
    {
        var result = new Dictionary<string, List<string>>();

        foreach (string cmd in commands)
        {
            string key = "other";

            if (cmd.Contains("pm "))
            {
                var parts = cmd.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 4)
                {
                    string action = parts[1];
                    string package = parts[parts.Length - 1];

                    key = $"{action}_{package}";
                }
            }
            else if (cmd.Contains("settings put"))
            {
                key = "settings";
            }
            else if (cmd.Contains("am "))
            {
                var parts = cmd.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 3)
                {
                    key = $"am_{parts[1]}";
                }
            }

            if (!result.ContainsKey(key))
            {
                result[key] = new List<string>();
            }
            result[key].Add(cmd);
        }

        return result;
    }

    public async Task<string?> ExecuteShellSession(string[] commands, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string pathadb = Path.Combine(Directory.GetCurrentDirectory(), ADB_EXECUTABLE_PATH);

        if (!VerifyAndEnsureAdbExecutable())
        {
            return string.Empty;
        }

        return await Task.Run(async () =>
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = pathadb,
                    Arguments = "shell",
                    RedirectStandardInput = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using Process adbProcess = new Process { StartInfo = startInfo };
                adbProcess.Start();

                ProcessManager.RegisterProcess(adbProcess);

                StringBuilder outputBuilder = new StringBuilder();

                using (StreamWriter inputWriter = adbProcess.StandardInput)
                {
                    foreach (string cmd in commands)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        inputWriter.WriteLine(cmd);
                        await Task.Delay(10, cancellationToken);
                    }
                    inputWriter.WriteLine("exit");
                }

                string output = await adbProcess.StandardOutput.ReadToEndAsync();
                string error = await adbProcess.StandardError.ReadToEndAsync();

                adbProcess.WaitForExit();

                if (!string.IsNullOrEmpty(error) && !error.Contains("* daemon"))
                {
                    return null;
                }

                return output;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch
            {
                return string.Empty;
            }
        }, cancellationToken);
    }

    public void UploadFile(string localFileName, string remoteFileName, int remoteFilePermissions = 1638)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        AdbFileInfo fileInfo = GetFileInfo(remoteFileName);
        if (fileInfo.IsDirectory || fileInfo.IsSymbolicLink)
        {
            remoteFileName = AdbHelpers.CombinePath(remoteFileName, Path.GetFileName(localFileName));
        }
        using AdbSocket adbSocket = GetSocket();
        string? text = SendSyncCommand(adbSocket, "SEND", $"{remoteFileName},{remoteFilePermissions}", readResponse: false);
        FileInfo fileInfo2 = new FileInfo(localFileName);
        int num = (int)fileInfo2.Length;
        using (FileStream fileStream = File.OpenRead(localFileName))
        {
            byte[] array = new byte[65536];
            while (num > 0)
            {
                int num2 = ((num < array.Length) ? num : array.Length);
                int bytesRead = fileStream.Read(array, 0, num2);
                if (bytesRead != num2)
                {
                    throw new IOException("Failed to read expected number of bytes from file");
                }
                adbSocket.WriteString("DATA");
                adbSocket.WriteInt32(num2);
                adbSocket.Write(array, num2);
                num -= num2;
            }
        }
        adbSocket.WriteString("DONE");
        adbSocket.WriteInt32(AdbHelpers.ToUnixTime(fileInfo2.LastWriteTime));
        text = adbSocket.ReadString(4);
        if (!string.Equals(text, "OKAY"))
        {
            throw new AdbInvalidResponseException(text ?? "Unknown error");
        }
    }

    private string? SendSyncCommand(AdbSocket adbSocket, string command, string parameter, bool readResponse = true)
    {
        if (parameter == null)
        {
            throw new ArgumentNullException("parameter");
        }
        SetDevice(adbSocket);
        adbSocket.SendCommand("sync:");
        return adbSocket.SendSyncCommand(command, parameter, readResponse);
    }

    public void DeleteFile(string remoteFileName)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        ExecuteRemoteCommand("rm -f " + remoteFileName);
    }

    public void InstallApplication(string localFileName, bool installOnSdCard)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string fileName = Path.GetFileName(localFileName);
        string text = (installOnSdCard ? ("/sdcard/tmp/" + fileName) : ("/data/local/tmp/" + fileName));
        string text2 = (installOnSdCard ? "-s" : "");
        if (GetFileInfo(text).IsFile)
        {
            DeleteFile(text);
        }
        UploadFile(localFileName, text);
        ExecutePm("install " + text2 + " " + text);
        DeleteFile(text);
    }

    public void UninstallApplication(string applicationName, bool keepDataAndCache = false)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string text = (keepDataAndCache ? "-k" : "");
        ExecutePm("uninstall " + text + " " + applicationName);
    }

    public AdbAppInfo[] GetInstalledApplications()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AdbClient));

        string[] array = ExecuteRemoteCommand("pm list packages -f");
        List<AdbAppInfo> list = new List<AdbAppInfo>();
        string[] array2 = array;
        foreach (string text in array2)
        {
            Match match = Regex.Match(text, "package:(.+?)=(.+)");
            if (match.Groups.Count == 3)
            {
                string value = match.Groups[1].Value;
                AdbAppType type = (value.StartsWith("/system/app/") ? AdbAppType.System : (value.StartsWith("/system/priv-app/") ? AdbAppType.Privileged : AdbAppType.ThirdParty));
                AdbAppLocation location = ((!value.StartsWith("/system/") && !value.StartsWith("/data/")) ? AdbAppLocation.ExternalMemory : AdbAppLocation.InternalMemory);
                AdbAppInfo item = new AdbAppInfo(match.Groups[2].Value, value, type, location);
                list.Add(item);
                continue;
            }
            throw new Exception(text);
        }
        return list.ToArray();
    }

    private void ExecutePm(string commandLine)
    {
        string[] array = ExecuteRemoteCommand("pm " + commandLine);
        if (array != null && array.Length != 0)
        {
            string text = array[array.Length - 1];
            if (!text.Equals("Success"))
            {
                Match match = Regex.Match(text, "\\[(.+?)]");
                throw new Exception((2 == match.Groups.Count) ? match.Groups[1].Value : text);
            }
            return;
        }
        throw new Exception("Wrong pm output");
    }

    private void SetDevice(AdbSocket adbSocket)
    {
        if (string.IsNullOrEmpty(DeviceSerialNumber))
        {
            throw new AdbException("No device selected. Please ensure a device is connected and try again.");
        }
        adbSocket.SendCommand("host:transport:" + DeviceSerialNumber);
    }

    private AdbSocket GetSocket()
    {
        return new AdbSocket(ServerHost, ServerPort);
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            try { StopServer(); } catch { }
            ProcessManager.TerminateAllProcesses();
        }
        catch 
        {
        }

        _disposed = true;
    }

    ~AdbClient()
    {
        Dispose();
    }

    private bool ValidateResourceIntegrity(string filePath, long expectedMinSize = 1024)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            var fileInfo = new FileInfo(filePath);
            
            // Check if file has reasonable size
            if (fileInfo.Length < expectedMinSize)
                return false;

            // Try to open and read first few bytes to ensure file is not corrupted
            using (var stream = File.OpenRead(filePath))
            {
                if (stream.Length == 0)
                    return false;

                // Read first few bytes to verify file accessibility
                byte[] buffer = new byte[Math.Min(1024, (int)stream.Length)];
                int bytesRead = stream.Read(buffer, 0, buffer.Length);
                
                return bytesRead > 0;
            }
        }
        catch
        {
            return false;
        }
    }

    private void CleanupTempFiles(string pattern)
    {
        try
        {
            string tempPath = Path.GetTempPath();
            var tempFiles = Directory.GetFiles(tempPath, pattern, SearchOption.TopDirectoryOnly);
            
            foreach (string file in tempFiles)
            {
                try
                {
                    var fileInfo = new FileInfo(file);
                    // Only delete files older than 1 hour to avoid conflicts
                    if (DateTime.Now - fileInfo.CreationTime > TimeSpan.FromHours(1))
                    {
                        File.Delete(file);
                    }
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
        catch
        {
            // Ignore cleanup errors
        }
    }
}

