using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Threading;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library.Security
{
    public class SecurityChecker : IDisposable
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr OpenProcess(int dwDesiredAccess, [MarshalAs(UnmanagedType.Bool)] bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool TerminateProcess(IntPtr hProcess, uint uExitCode);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateToolhelp32Snapshot(uint dwFlags, uint th32ProcessID);

        [DllImport("kernel32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool Process32FirstW(IntPtr hSnapshot, ref PROCESSENTRY32W lppe);

        [DllImport("kernel32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool Process32NextW(IntPtr hSnapshot, ref PROCESSENTRY32W lppe);

        [DllImport("psapi.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern uint GetModuleFileNameExW(IntPtr hProcess, IntPtr hModule, [Out] StringBuilder lpBaseName, [In] uint nSize);

        [DllImport("version.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool GetFileVersionInfoW(string lptstrFilename, int dwHandle, int dwLen, byte[] lpData);

        [DllImport("version.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern int GetFileVersionInfoSizeW(string lptstrFilename, out int lpdwHandle);

        [DllImport("version.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool VerQueryValueW(byte[] pBlock, string lpSubBlock, out IntPtr lplpBuffer, out uint puLen);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern void ExitProcess(uint uExitCode);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        private struct PROCESSENTRY32W
        {
            public uint dwSize;
            public uint cntUsage;
            public uint th32ProcessID;
            public IntPtr th32DefaultHeapID;
            public uint th32ModuleID;
            public uint cntThreads;
            public uint th32ParentProcessID;
            public int pcPriClassBase;
            public uint dwFlags;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 260)]
            public string szExeFile;
        }

        private const int PROCESS_TERMINATE = 0x0001;
        private const int PROCESS_QUERY_INFORMATION = 0x0400;
        private const int PROCESS_VM_READ = 0x0010;
        private const uint TH32CS_SNAPPROCESS = 0x00000002;
        private const int MAX_PATH = 260;

        private System.Threading.Timer? _securityTimer;
        private readonly Window _parentWindow;
        private volatile bool _isExiting;
        private static readonly object _syncLock = new();
        private int _securityCheckInProgress = 0;

        private static readonly HashSet<string> UsbRedirectorProcesses = new(StringComparer.OrdinalIgnoreCase)
        {
           "usbredirector-technician.exe",
           "usbredirector.exe",
           "technician.exe",
           "usbredirectortechssrv.exe",
           "usbtechsh.exe",
           "usbclient.exe",
           "usbclientservice.exe",
           "usbtechclt.exe",
           "usbredirectortech.exe",
           "usbredirectorpro.exe",
           "usbmanager.exe",
           "usb-redirector.exe",
           "usb-redirector-tech.exe",
           "usbman.exe",
           "usbredir.exe",
           "usbredirectortechnician.exe",
           "usb redirector technician.exe",
           "usb redirector.exe"
        };

        private static readonly HashSet<string> SuspiciousProcesses = new(StringComparer.OrdinalIgnoreCase)
        {
            "cheatengine.exe",
            "cheatengine-x86_64.exe",
            "cheat engine.exe",
            "ida.exe",
            "ida64.exe",
            "beamer.exe",
            "x64dbg.exe",
            "x96dbg.exe",
            "x32dbg.exe",
            "dnspy.exe",
            "binaryninja.exe",
            "ollydbg.exe",
            "extremedumper.exe",
            "hxd.exe",
            "die.exe",
            "luaclient-i386.dll",
            "scylla_x86.exe",
            "scylla_x64.exe",
            "scylla.exe",
            "de4dot.exe",
            "ilspy.exe",
            "reflector.exe",
            "dotpeek.exe",
            "megadumper.exe",

            "wireshark.exe",
            "fiddler.exe",
            "fiddler everywhere.exe",
            "mitmproxy.exe",
            "burp.exe",
            "burpsuite.exe",
            "charles.exe",
            "http debugger.exe",
            "httpanalyzerv7.exe",
            "postman.exe",
            "httptoolkit.exe",
            "proxycap.exe",
            "paessler packet sniffer.exe",
            "smartsniff.exe",
            "netcapture.exe",
            "tcpdump.exe",
            "httpwatch.exe",
            "owasp zap.exe",
            "webscarab.exe",
            "paros.exe",
            "sslsplit.exe",
            "insomnia.exe",
            "netsparker.exe",

            "process hacker.exe",
            "processhacker.exe",
            "processhacker2.exe",
            "process explorer.exe",
            "system informer.exe",
            "systeminformer.exe",
            "resource hacker.exe",
            "resourcehacker.exe",
            "reshacker.exe",
            "regfromapp.exe",
            "regshot.exe",
            "registry changes view.exe",
            "registrychangesview.exe",
            "regworks.exe",
            "systracer.exe",
            "whatchanged.exe",
            "registry finder.exe",
            "process monitor.exe",
            "lunar engine.exe",
            "directory monitor.exe",
            "directorymonitor.exe",
            "file grab.exe",
            "device hackerp.exe",
            "speedhack-i386.dll",

            "terminal.exe",
            "terminalnew.exe",
            "gdb.exe",
            "windbg.exe",
            "immunity debugger.exe",
            "immunitydebugger.exe",

            "stringdecryptor.exe",
            "codecracker.exe",
            "simpleassemblyexplorer.exe",
            "simpleassembly.exe",
            "dissect code.exe",
            "string search.exe",
            "exeinfope.exe",
            "cff explorer.exe",
            "ghidra.exe",
            "firebug.exe",
            "jetbrains dotpeek.exe",

            "extreme injector.exe",
            "ksdumper.exe",
            "memory viewer.exe",
            "cheat engine-i386.exe",
            "cheat engine-x86_64.exe",
            "cosmos.exe",
            "sandboxie.exe",
            "injector.exe",

            "USBlyzer.exe",
            "USBCapturer.exe",
            "USBLogView.exe",
            "USBDeview.exe",
            "USB Monitor Pro.exe",
            "USB Monitor.exe",
            "USBPcap.exe",
            "SoftPerfect USB Analyzer.exe",
            "USB Analyzer.exe",
            "BusDog.exe",
            "USB Snoopy.exe",
            "SniffUSB.exe",
            "USBSnoop.exe",
            "HHD Device Monitoring Studio.exe",
            "USB Monitor Plus.exe",
            "USB Detective.exe",
            "USB Protocol Analyzer.exe",
            "USB Device Viewer.exe",
            "USB Port Monitor.exe",
            "USBObserver.exe",
            "USBSnifferPro.exe",
            "USBSniff.exe",
            "USB Packet Viewer.exe",
            "DeviceMon.exe",
            "device monitor.exe",
            "usbmonitor.exe",
            "usbtrace.exe",
            "usbsniff.exe",
            "usbcap.exe",
            "usblogview.exe",
            "usbmonitorpro.exe",
            "usb-monitor.exe",
            "usbanalyzer.exe",
            "busdog64.exe",
            "busdog32.exe",
            "USBPcapCMD.exe",
            "DeviceMonitor.exe",
            "DeviceExplorer.exe",

            "HTTPDebuggerSvc.exe",
            "HTTPDebuggerUI.exe",
            "FileActivityWatch.exe",
            "netcheat.exe",
            "DotNetDataCollector32.exe",
            "DotNetDataCollector64.exe",
            "solarwinds.exe"
        };

        public SecurityChecker(Window parentWindow)
        {
            _parentWindow = parentWindow;
            _isExiting = false;
        }

        public void StartMonitoring()
        {
            PerformSecurityCheck();
            _securityTimer = new System.Threading.Timer(SecurityTimerCallback, null, TimeSpan.FromSeconds(3), TimeSpan.FromSeconds(3));
        }

        public void StopMonitoring()
        {
            _securityTimer?.Dispose();
            _securityTimer = null;
        }

        private void SecurityTimerCallback(object? state)
        {
            if (_isExiting) return;

            if (Interlocked.CompareExchange(ref _securityCheckInProgress, 1, 0) == 0)
            {
                ThreadPool.QueueUserWorkItem(_ =>
                {
                    try
                    {
                        PerformSecurityCheckInternal();
                    }
                    finally
                    {
                        Interlocked.Exchange(ref _securityCheckInProgress, 0);
                    }
                });
            }
        }

        public void PerformSecurityCheck()
        {
            if (_isExiting) return;
            ThreadPool.QueueUserWorkItem(_ => PerformSecurityCheckInternal());
        }

        private void PerformSecurityCheckInternal()
        {
            if (_isExiting) return;

            if (CheckForUsbRedirector(out string redirectorName, out int redirectorProcessId))
            {
                KillProcessById(redirectorProcessId);
                Dispatcher.UIThread.InvokeAsync(() => ShowUsbRedirectorAlert(redirectorName));
                return;
            }

            if (IsDebuggerDetected(true, out string debuggerName))
            {
                Dispatcher.UIThread.InvokeAsync(() => ShowAlertAndExit("Security Alert - Debugger Detected",
                    $"Security Alert: A debugging tool has been detected!\n\n" +
                    $"Detected Debugger: {debuggerName}\n\n" +
                    $"This software cannot run while debugging tools are active.\n\n" +
                    $"The application will now close for security reasons."));
                return;
            }

            CheckForSuspiciousProcesses();
        }

        private bool CheckForUsbRedirector(out string processName, out int processId)
        {
            processName = string.Empty;
            processId = -1;

            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows) || _isExiting)
                return false;

            IntPtr hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == IntPtr.Zero || hSnapshot == new IntPtr(-1))
                return false;

            try
            {
                PROCESSENTRY32W pe32 = new()
                {
                    dwSize = (uint)Marshal.SizeOf<PROCESSENTRY32W>()
                };

                if (Process32FirstW(hSnapshot, ref pe32))
                {
                    do
                    {
                        try
                        {
                            string procName = pe32.szExeFile;
                            uint pid = pe32.th32ProcessID;

                            if (pid == (uint)Process.GetCurrentProcess().Id)
                                continue;

                            if (UsbRedirectorProcesses.Contains(procName.ToLower()))
                            {
                                processName = procName;
                                processId = (int)pid;
                                return true;
                            }

                            IntPtr hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, (int)pid);
                            if (hProcess != IntPtr.Zero)
                            {
                                try
                                {
                                    string originalFilename = GetOriginalFilename(hProcess);
                                    if (!string.IsNullOrEmpty(originalFilename) && UsbRedirectorProcesses.Contains(originalFilename.ToLower()))
                                    {
                                        processName = procName;
                                        processId = (int)pid;
                                        return true;
                                    }
                                }
                                finally
                                {
                                    CloseHandle(hProcess);
                                }
                            }
                        }
                        catch
                        {
                            // Ignore process access errors
                        }
                    } while (Process32NextW(hSnapshot, ref pe32));
                }
            }
            finally
            {
                CloseHandle(hSnapshot);
            }

            return false;
        }

        private void ShowUsbRedirectorAlert(string redirectorName)
        {
            if (_isExiting) return;

            lock (_syncLock)
            {
                if (_isExiting) return;
                _isExiting = true;
            }

            StopMonitoring();

            Dispatcher.UIThread.InvokeAsync(async () =>
            {
                if (_parentWindow != null)
                {
                    _parentWindow.IsEnabled = false;
                }

                var messageBox = new Window
                {
                    Title = "USB Redirector Terminated",
                    Width = 460,
                    Height = 340,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    CanResize = false,
                    SystemDecorations = SystemDecorations.Full,
                    ShowInTaskbar = true,
                    Topmost = true,
                    Background = new SolidColorBrush(Color.Parse("#2A2A2A")),
                    WindowState = WindowState.Normal,
                    ExtendClientAreaToDecorationsHint = false,
                    ExtendClientAreaChromeHints = ExtendClientAreaChromeHints.NoChrome,
                    ExtendClientAreaTitleBarHeightHint = -1
                };

                var mainBorder = new Border
                {
                    Background = new SolidColorBrush(Color.Parse("#323232")),
                    CornerRadius = new CornerRadius(8),
                    BoxShadow = new BoxShadows(new BoxShadow
                    {
                        OffsetX = 0,
                        OffsetY = 2,
                        Blur = 10,
                        Spread = 0,
                        Color = Color.Parse("#20000000")
                    }),
                    Margin = new Thickness(12)
                };

                var panel = new StackPanel
                {
                    Margin = new Thickness(20),
                    Spacing = 15
                };

                var headerPanel = new StackPanel
                {
                    Orientation = Avalonia.Layout.Orientation.Horizontal,
                    Spacing = 10,
                    Margin = new Thickness(0, 0, 0, 0),
                    HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center
                };

                var warningIcon = new TextBlock
                {
                    Text = "⚠",
                    FontSize = 20,
                    Foreground = new SolidColorBrush(Color.Parse("#FFB900")),
                    VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                    FontWeight = FontWeight.Bold
                };

                var titleBlock = new TextBlock
                {
                    Text = "USB Redirector Not Compatible",
                    FontWeight = FontWeight.Bold,
                    FontSize = 18,
                    Foreground = new SolidColorBrush(Color.Parse("#FF5C5C")),
                    VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
                };

                headerPanel.Children.Add(warningIcon);
                headerPanel.Children.Add(titleBlock);

                var separator = new Separator
                {
                    Height = 1,
                    Margin = new Thickness(0, 12, 0, 12),
                    Background = new SolidColorBrush(Color.Parse("#444444")),
                };

                var textBlock = new TextBlock
                {
                    Text = $"This application cannot run with USB Redirector.\n\n" +
                           $"Please uninstall USB Redirector first, then reconnect your device directly to this computer.\n\n" +
                           $"The application will close automatically.",
                    TextWrapping = TextWrapping.Wrap,
                    Foreground = new SolidColorBrush(Color.Parse("#FFFFFF")),
                    Margin = new Thickness(0, 5, 0, 10),
                    FontSize = 14,
                    TextAlignment = Avalonia.Media.TextAlignment.Center,
                    LineHeight = 20
                };

                var button = new Button
                {
                    Content = "OK",
                    Width = 120,
                    Height = 36,
                    HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                    HorizontalContentAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                    VerticalContentAlignment = Avalonia.Layout.VerticalAlignment.Center,
                    Background = new SolidColorBrush(Color.Parse("#555555")),
                    Foreground = new SolidColorBrush(Color.Parse("#FFFFFF")),
                    BorderThickness = new Thickness(0),
                    CornerRadius = new CornerRadius(2),
                    FontWeight = FontWeight.SemiBold,
                    FontSize = 14,
                    Margin = new Thickness(0, 5, 0, 0),
                    Padding = new Thickness(0),
                    Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Hand)
                };

                button.PointerEntered += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#666666"));
                };

                button.PointerExited += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#555555"));
                };

                button.PointerPressed += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#444444"));
                };

                button.PointerReleased += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#666666"));
                };

                button.Click += (s, e) => ForceExit();

                panel.Children.Add(headerPanel);
                panel.Children.Add(separator);
                panel.Children.Add(textBlock);
                panel.Children.Add(button);

                mainBorder.Child = panel;
                messageBox.Content = mainBorder;

                messageBox.Closed += (s, e) => ForceExit();

                try
                {
                    await messageBox.ShowDialog(_parentWindow!);
                }
                catch
                {
                    ForceExit();
                }
            });

            Task.Delay(5000).ContinueWith(_ => ForceExit());
        }

        private bool IsDebuggerDetected(bool terminate, out string debuggerName)
        {
            debuggerName = string.Empty;

            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return false;

            if (IsDebuggerPresent())
            {
                debuggerName = "Windows Debugger";
                if (terminate)
                {
                    ForceExit();
                }
                return true;
            }

            bool isRemoteDebuggerPresent = false;
            if (CheckRemoteDebuggerPresent(Process.GetCurrentProcess().Handle, ref isRemoteDebuggerPresent) &&
                isRemoteDebuggerPresent)
            {
                debuggerName = "Remote Debugger";
                if (terminate)
                {
                    ForceExit();
                }
                return true;
            }

            IntPtr hSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnap == IntPtr.Zero || hSnap == new IntPtr(-1))
                return false;

            try
            {
                PROCESSENTRY32W pe32 = new()
                {
                    dwSize = (uint)Marshal.SizeOf<PROCESSENTRY32W>()
                };

                if (Process32FirstW(hSnap, ref pe32))
                {
                    do
                    {
                        string processName = pe32.szExeFile;
                        uint processId = pe32.th32ProcessID;

                        IntPtr hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, (int)processId);
                        if (hProcess != IntPtr.Zero)
                        {
                            try
                            {
                                string originalFilename = GetOriginalFilename(hProcess);
                                if (!string.IsNullOrEmpty(originalFilename) && SuspiciousProcesses.Contains(originalFilename.ToLower()))
                                {
                                    debuggerName = processName;
                                    if (terminate)
                                    {
                                        TerminateProcess(hProcess, 0);
                                        KillProcessById((int)processId);
                                        ForceExit();
                                    }
                                    return true;
                                }
                            }
                            finally
                            {
                                CloseHandle(hProcess);
                            }
                        }

                        if (SuspiciousProcesses.Contains(processName.ToLower()))
                        {
                            debuggerName = processName;
                            if (terminate)
                            {
                                KillProcessById((int)processId);
                                ForceExit();
                            }
                            return true;
                        }
                    } while (Process32NextW(hSnap, ref pe32));
                }
            }
            catch
            {
                // Ignore debugger detection errors
            }
            finally
            {
                CloseHandle(hSnap);
            }

            return false;
        }

        private string GetOriginalFilename(IntPtr hProcess)
        {
            try
            {
                StringBuilder path = new(MAX_PATH);
                if (GetModuleFileNameExW(hProcess, IntPtr.Zero, path, (uint)path.Capacity) > 0)
                {
                    string filePath = path.ToString();
                    if (File.Exists(filePath))
                    {
                        int handle = 0;
                        int size = GetFileVersionInfoSizeW(filePath, out handle);
                        if (size > 0)
                        {
                            byte[] buffer = new byte[size];
                            if (GetFileVersionInfoW(filePath, handle, size, buffer))
                            {
                                string originalFilename = GetStringFileInfo(buffer, "\\StringFileInfo\\040904B0\\OriginalFilename");
                                if (string.IsNullOrEmpty(originalFilename))
                                {
                                    originalFilename = GetStringFileInfo(buffer, "\\StringFileInfo\\000004B0\\OriginalFilename");
                                }

                                if (string.IsNullOrEmpty(originalFilename))
                                {
                                    originalFilename = GetStringFileInfo(buffer, "\\StringFileInfo\\040904E4\\OriginalFilename");
                                }

                                if (string.IsNullOrEmpty(originalFilename))
                                {
                                    originalFilename = Path.GetFileName(filePath);
                                }

                                return originalFilename;
                            }
                        }
                    }
                }
            }
            catch
            {
                // Ignore file info errors
            }
            return string.Empty;
        }

        private string GetStringFileInfo(byte[] buffer, string subBlock)
        {
            IntPtr ptr = IntPtr.Zero;
            uint len = 0;

            if (VerQueryValueW(buffer, subBlock, out ptr, out len) && len > 0 && ptr != IntPtr.Zero)
            {
                return Marshal.PtrToStringUni(ptr) ?? string.Empty;
            }
            return string.Empty;
        }

        private bool CheckForSuspiciousProcesses()
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows) || _isExiting)
                return false;

            IntPtr hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == IntPtr.Zero || hSnapshot == new IntPtr(-1))
                return false;

            try
            {
                PROCESSENTRY32W pe32 = new()
                {
                    dwSize = (uint)Marshal.SizeOf<PROCESSENTRY32W>()
                };

                if (Process32FirstW(hSnapshot, ref pe32))
                {
                    do
                    {
                        try
                        {
                            string processName = pe32.szExeFile;
                            uint processId = pe32.th32ProcessID;

                            if (processId == (uint)Process.GetCurrentProcess().Id)
                                continue;

                            IntPtr hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, (int)processId);
                            if (hProcess != IntPtr.Zero)
                            {
                                try
                                {
                                    string originalFilename = GetOriginalFilename(hProcess);
                                    if (!string.IsNullOrEmpty(originalFilename) && SuspiciousProcesses.Contains(originalFilename.ToLower()))
                                    {
                                        TerminateProcess(hProcess, 0);
                                        KillProcessById((int)processId);

                                        Dispatcher.UIThread.InvokeAsync(() => ShowAlertAndExit("Security Alert - Debugging Tool Detected",
                                            $"Security Alert: A debugging/monitoring tool has been detected!\n\n" +
                                            $"Application Name: {processName}\n\n" +
                                            $"This software cannot run while these tools are active.\n\n" +
                                            $"Please remove or close {processName}, then restart your computer.\n" +
                                            $"The application will now close for security reasons."));
                                        return true;
                                    }
                                }
                                finally
                                {
                                    CloseHandle(hProcess);
                                }
                            }

                            if (SuspiciousProcesses.Contains(processName.ToLower()))
                            {
                                KillProcessById((int)processId);

                                Dispatcher.UIThread.InvokeAsync(() => ShowAlertAndExit("Security Alert - Debugging Tool Detected",
                                    $"Security Alert: A debugging/monitoring tool has been detected!\n\n" +
                                    $"Application Name: {processName}\n\n" +
                                    $"This software cannot run while these tools are active.\n\n" +
                                    $"Please remove or close {processName}, then restart your computer.\n" +
                                    $"The application will now close for security reasons."));
                                return true;
                            }
                        }
                        catch
                        {
                            // Ignore process access errors
                        }
                    } while (Process32NextW(hSnapshot, ref pe32));
                }
            }
            finally
            {
                CloseHandle(hSnapshot);
            }

            return false;
        }

        private void KillProcessById(int processId)
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    IntPtr hProcess = OpenProcess(PROCESS_TERMINATE, false, processId);
                    if (hProcess != IntPtr.Zero)
                    {
                        try
                        {
                            TerminateProcess(hProcess, 0);
                        }
                        finally
                        {
                            CloseHandle(hProcess);
                        }
                    }

                    try
                    {
                        Process proc = Process.GetProcessById(processId);
                        proc.Kill(true);
                    }
                    catch
                    {
                        // Ignore process kill errors
                    }
                }
                else
                {
                    try
                    {
                        Process proc = Process.GetProcessById(processId);
                        proc.Kill();
                    }
                    catch
                    {
                        // Ignore process kill errors
                    }
                }
            }
            catch
            {
                // Ignore process termination errors
            }
        }

        private void ShowAlertAndExit(string title, string message)
        {
            if (_isExiting) return;

            lock (_syncLock)
            {
                if (_isExiting) return;
                _isExiting = true;
            }

            StopMonitoring();

            Dispatcher.UIThread.InvokeAsync(async () =>
            {
                if (_parentWindow != null)
                {
                    _parentWindow.IsEnabled = false;
                }

                var messageBox = new Window
                {
                    Title = title,
                    Width = 400,
                    Height = 260,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    CanResize = false,
                    SystemDecorations = SystemDecorations.Full,
                    ShowInTaskbar = true,
                    Topmost = true,
                    Background = new SolidColorBrush(Color.Parse("#2A2A2A")),
                    WindowState = WindowState.Normal,
                    ExtendClientAreaToDecorationsHint = false,
                    ExtendClientAreaChromeHints = ExtendClientAreaChromeHints.NoChrome,
                    ExtendClientAreaTitleBarHeightHint = -1
                };

                var mainBorder = new Border
                {
                    Background = new SolidColorBrush(Color.Parse("#323232")),
                    CornerRadius = new CornerRadius(8),
                    BoxShadow = new BoxShadows(new BoxShadow
                    {
                        OffsetX = 0,
                        OffsetY = 2,
                        Blur = 10,
                        Spread = 0,
                        Color = Color.Parse("#10000000")
                    }),
                    Margin = new Thickness(12)
                };

                var panel = new StackPanel
                {
                    Margin = new Thickness(25),
                    Spacing = 20
                };

                var titleBlock = new TextBlock
                {
                    Text = title,
                    FontWeight = FontWeight.Bold,
                    FontSize = 18,
                    Foreground = new SolidColorBrush(Color.Parse("#FF5C5C")),
                    HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center
                };

                var textBlock = new TextBlock
                {
                    Text = message,
                    TextWrapping = TextWrapping.Wrap,
                    Foreground = new SolidColorBrush(Color.Parse("#FFFFFF")),
                    Margin = new Thickness(0, 5, 0, 10)
                };

                var buttonBorder = new Border
                {
                    Background = new SolidColorBrush(Color.Parse("#FF5C5C")),
                    CornerRadius = new CornerRadius(6),
                    Width = 120,
                    Height = 40,
                    HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center
                };

                var button = new Button
                {
                    Content = "OK",
                    Width = 130,
                    Height = 42,
                    HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                    HorizontalContentAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                    VerticalContentAlignment = Avalonia.Layout.VerticalAlignment.Center,
                    Background = new SolidColorBrush(Color.Parse("#E74C3C")),
                    Foreground = new SolidColorBrush(Color.Parse("#FFFFFF")),
                    BorderThickness = new Thickness(0),
                    CornerRadius = new CornerRadius(8),
                    FontWeight = FontWeight.Bold,
                    FontSize = 15,
                    Padding = new Thickness(0),
                    Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Hand)
                };

                button.PointerEntered += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#F75C4C"));
                };

                button.PointerExited += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#E74C3C"));
                };

                button.PointerPressed += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.Parse("#C0392B"));
                };

                button.Click += (s, e) => ForceExit();
                buttonBorder.Child = button;

                panel.Children.Add(titleBlock);
                panel.Children.Add(textBlock);
                panel.Children.Add(buttonBorder);
                panel.Children.Add(button);
                mainBorder.Child = panel;
                messageBox.Content = mainBorder;

                messageBox.Closed += (s, e) => ForceExit();

                try
                {
                    await messageBox.ShowDialog(_parentWindow!);
                }
                catch
                {
                    ForceExit();
                }
            });

            Task.Delay(3000).ContinueWith(_ => ForceExit());
        }

        private void ForceExit()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    ExitProcess(0);
                }
                else
                {
                    Environment.Exit(0);
                }
            }
            catch
            {
                Process.GetCurrentProcess().Kill();
            }
        }

        public static string SimpleEncrypt(string plainText)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(plainText);
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = (byte)(bytes[i] ^ 0x5A);
            }
            return Convert.ToBase64String(bytes);
        }

        public static string SimpleDecrypt(string encryptedText)
        {
            byte[] bytes = Convert.FromBase64String(encryptedText);
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = (byte)(bytes[i] ^ 0x5A);
            }
            return Encoding.UTF8.GetString(bytes);
        }

        public void Dispose()
        {
            _securityTimer?.Dispose();
            _securityTimer = null;
            GC.SuppressFinalize(this);
        }
    }
}