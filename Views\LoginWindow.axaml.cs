using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using SamsungTool.Library.Security;
using SamsungTool.Views;
using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace SamsungTool
{
    public partial class LoginWindow : Window, IDisposable
    {
        private bool _isLoggingIn = false;
        private bool _isPasswordVisible = false;
        private bool _isUpdating = false;
        private static readonly HttpClient _httpClient = new HttpClient();
        private readonly AES256Encryption _encryption = new AES256Encryption();
        private SecurityChecker? _securityChecker;
        private bool _disposed = false;
        private string _currentHWID = string.Empty;

        private static readonly string AppDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SamsungTool");
        private static readonly string CredentialsFile = Path.Combine(AppDataPath, "user.dat");
        private static readonly string UpdateUrl = "https://samsungtool.service-app.org/updates/AutoUpdater.xml";
        private static readonly string TempUpdatePath = Path.Combine(Path.GetTempPath(), "SamsungToolUpdate");

        public static string? AuthToken { get; private set; }
        public static DateTime TokenExpiresAt { get; private set; }
        public static bool UpdateRequired { get; private set; } = false;
        public static string? SavedUsername { get; private set; }

        public LoginWindow()
        {
            InitializeComponent();
            SetupControls();
            LoadCredentials();
            _ = LoadHWIDAsync();

            _securityChecker = new SecurityChecker(this);
            _securityChecker.StartMonitoring();

            _ = Task.Run(async () => await CheckForUpdatesAsync().ConfigureAwait(false));

            // Start entrance animations after window is loaded
            this.Opened += OnWindowOpened;
        }

        private async void OnWindowOpened(object? sender, EventArgs e)
        {
            await StartEntranceAnimationsAsync();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void SetupControls()
        {
            var loginButton = this.FindControl<Button>("LoginButton");
            var showPasswordButton = this.FindControl<Button>("ShowPasswordButton");
            var copyHwidButton = this.FindControl<Button>("CopyHWIDButton");
            var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");
            var usernameTextBox = this.FindControl<TextBox>("UsernameTextBox");
            var websiteButton = this.FindControl<Button>("WebsiteButton");

            if (loginButton != null)
                loginButton.Click += OnLoginClicked;
            
            if (showPasswordButton != null)
                showPasswordButton.Click += OnShowPasswordClicked;
            
            if (copyHwidButton != null)
                copyHwidButton.Click += OnCopyHWIDClicked;

            if (passwordTextBox != null)
            {
                passwordTextBox.KeyDown += (s, e) =>
                {
                    if (e.Key == Avalonia.Input.Key.Enter && !_isUpdating)
                        OnLoginClicked(s, new RoutedEventArgs());
                };
            }

            if (usernameTextBox != null)
            {
                usernameTextBox.KeyDown += (s, e) =>
                {
                    if (e.Key == Avalonia.Input.Key.Enter && !_isUpdating)
                    {
                        var pwdBox = this.FindControl<TextBox>("PasswordTextBox");
                        pwdBox?.Focus();
                    }
                };
            }

            if (websiteButton != null)
            {
                websiteButton.Click += (s, e) =>
                {
                    try
                    {
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = "https://t.me/William33821",
                            UseShellExecute = true
                        });
                    }
                    catch
                    {
                        // Silently ignore process start failures
                    }
                };
            }
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Checking for updates...", 0);
                });

                var currentVersion = GetCurrentVersion();
                var serverVersion = await GetServerVersionAsync().ConfigureAwait(false);

                if (serverVersion != null && IsNewerVersion(serverVersion, currentVersion))
                {
                    UpdateRequired = true;
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        ShowUpdateStatus($"Update found: v{serverVersion.Version}\nDownloading...", 10);
                    });

                    await StartUpdateProcess(serverVersion).ConfigureAwait(false);
                }
                else
                {
                    UpdateRequired = false;
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        HideUpdateStatus();
                    });
                }
            }
            catch
            {
                UpdateRequired = false;
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    HideUpdateStatus();
                });
            }
        }

        private void ShowUpdateStatus(string message, int progress)
        {
            try
            {
                _isUpdating = true;

                var updateOverlay = this.FindControl<Border>("UpdateOverlay");
                var updateTitle = this.FindControl<TextBlock>("UpdateTitle");
                var updateStatus = this.FindControl<TextBlock>("UpdateStatus");
                var updateProgressBar = this.FindControl<ProgressBar>("UpdateProgressBar");
                var updatePercentage = this.FindControl<TextBlock>("UpdatePercentage");

                if (updateOverlay != null) 
                    updateOverlay.IsVisible = true;

                if (updateTitle != null)
                {
                    updateTitle.Text = progress switch
                    {
                        0 => "CHECKING FOR UPDATES",
                        < 20 => "UPDATE FOUND",
                        < 80 => "DOWNLOADING UPDATE",
                        < 95 => "EXTRACTING FILES",
                        _ => "FINALIZING UPDATE"
                    };
                }

                if (updateStatus != null) 
                    updateStatus.Text = message;
                
                if (updateProgressBar != null) 
                    updateProgressBar.Value = progress;
                
                if (updatePercentage != null) 
                    updatePercentage.Text = $"{progress}%";

                SetControlsEnabled(false);
            }
            catch
            {
                // Silently handle UI update failures
            }
        }

        private void HideUpdateStatus()
        {
            try
            {
                _isUpdating = false;
                var updateOverlay = this.FindControl<Border>("UpdateOverlay");
                if (updateOverlay != null) 
                    updateOverlay.IsVisible = false;
                SetControlsEnabled(true);
            }
            catch
            {
                // Silently handle UI update failures
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            try
            {
                var loginButton = this.FindControl<Button>("LoginButton");
                var usernameTextBox = this.FindControl<TextBox>("UsernameTextBox");
                var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");
                var rememberCheckBox = this.FindControl<CheckBox>("RememberMeCheckBox");

                if (loginButton != null)
                    loginButton.IsEnabled = enabled;
                
                if (usernameTextBox != null)
                    usernameTextBox.IsEnabled = enabled;
                
                if (passwordTextBox != null)
                    passwordTextBox.IsEnabled = enabled;
                
                if (rememberCheckBox != null)
                    rememberCheckBox.IsEnabled = enabled;
            }
            catch
            {
                // Silently handle control update failures
            }
        }

        private static Version GetCurrentVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version ?? new Version(1, 0, 0, 0);
            }
            catch
            {
                return new Version(1, 0, 0, 0);
            }
        }

        private async Task<UpdateInfo?> GetServerVersionAsync()
        {
            try
            {
                using var response = await _httpClient.GetAsync(UpdateUrl).ConfigureAwait(false);
                response.EnsureSuccessStatusCode();

                var xmlContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                var doc = XDocument.Parse(xmlContent);
                var root = doc.Element("item");

                if (root != null)
                {
                    var versionElement = root.Element("version");
                    var urlElement = root.Element("url");
                    var changelogElement = root.Element("changelog");
                    var mandatoryElement = root.Element("mandatory");

                    if (versionElement?.Value != null && urlElement?.Value != null)
                    {
                        string changelog = "";
                        if (changelogElement?.Value != null && !string.IsNullOrEmpty(changelogElement.Value))
                        {
                            changelog = await GetChangelogContent(changelogElement.Value).ConfigureAwait(false);
                        }

                        return new UpdateInfo
                        {
                            Version = Version.Parse(versionElement.Value),
                            DownloadUrl = urlElement.Value,
                            Changelog = changelog,
                            IsMandatory = bool.Parse(mandatoryElement?.Value ?? "false")
                        };
                    }
                }
            }
            catch
            {
                // Silently handle version check failures
            }
            return null;
        }

        private async Task<string> GetChangelogContent(string changelogUrl)
        {
            try
            {
                using var response = await _httpClient.GetAsync(changelogUrl).ConfigureAwait(false);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                }
            }
            catch
            {
                // Silently handle changelog fetch failures
            }
            return "Update available";
        }

        private static bool IsNewerVersion(UpdateInfo serverInfo, Version currentVersion)
        {
            return serverInfo.Version > currentVersion;
        }

        private async Task StartUpdateProcess(UpdateInfo updateInfo)
        {
            try
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Preparing update...", 15);
                });

                if (Directory.Exists(TempUpdatePath))
                    Directory.Delete(TempUpdatePath, true);
                Directory.CreateDirectory(TempUpdatePath);

                var updateFilePath = Path.Combine(TempUpdatePath, "update.zip");

                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Downloading update...", 25);
                });

                await DownloadUpdateAsync(updateInfo.DownloadUrl, updateFilePath).ConfigureAwait(false);

                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Extracting files...", 80);
                });

                await ExtractUpdateAsync(updateFilePath).ConfigureAwait(false);

                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Restarting application...", 100);
                });

                await Task.Delay(1000).ConfigureAwait(false);
                RestartWithUpdate();
            }
            catch
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ShowUpdateStatus("Update failed", 0);
                });
                await Task.Delay(3000).ConfigureAwait(false);
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    HideUpdateStatus();
                });
                UpdateRequired = false;
            }
        }

        private async Task DownloadUpdateAsync(string downloadUrl, string filePath)
        {
            using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();

            var totalBytes = response.Content.Headers.ContentLength ?? 0;
            var downloadedBytes = 0L;

            using var contentStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

            var buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(false)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead).ConfigureAwait(false);
                downloadedBytes += bytesRead;

                if (totalBytes > 0)
                {
                    var progress = (int)(25 + ((downloadedBytes * 50) / totalBytes));
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        ShowUpdateStatus($"Downloading... {downloadedBytes / 1024}KB / {totalBytes / 1024}KB", progress);
                    });
                }
            }
        }

        private static async Task ExtractUpdateAsync(string zipFilePath)
        {
            await Task.Run(() =>
            {
                var extractPath = Path.Combine(TempUpdatePath, "extracted");
                ZipFile.ExtractToDirectory(zipFilePath, extractPath);
            }).ConfigureAwait(false);
        }

        private static void RestartWithUpdate()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                var currentExePath = currentProcess.MainModule?.FileName;
                
                if (string.IsNullOrEmpty(currentExePath))
                    return;

                var updateBatchPath = Path.Combine(TempUpdatePath, "update.bat");
                var extractedPath = Path.Combine(TempUpdatePath, "extracted");
                var appDirectory = Path.GetDirectoryName(currentExePath);

                if (string.IsNullOrEmpty(appDirectory))
                    return;

                var batchContent = $@"
@echo off
timeout /t 2 /nobreak >nul
taskkill /f /im ""{Path.GetFileName(currentExePath)}"" >nul 2>&1
timeout /t 1 /nobreak >nul
xcopy ""{extractedPath}\\*"" ""{appDirectory}\\"" /E /H /C /I /Y >nul 2>&1
timeout /t 1 /nobreak >nul
start """" ""{currentExePath}""
timeout /t 2 /nobreak >nul
rmdir /s /q ""{TempUpdatePath}"" >nul 2>&1
del ""%~f0"" >nul 2>&1
";

                File.WriteAllText(updateBatchPath, batchContent);

                var startInfo = new ProcessStartInfo
                {
                    FileName = updateBatchPath,
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                Process.Start(startInfo);
                Environment.Exit(0);
            }
            catch
            {
                Environment.Exit(1);
            }
        }

        private async Task StartEntranceAnimationsAsync()
        {
            try
            {
                await Dispatcher.UIThread.InvokeAsync(async () =>
                {
                    var mainGrid = this.FindControl<Grid>("MainGrid");
                    if (mainGrid != null)
                    {
                        mainGrid.Opacity = 1;
                        mainGrid.RenderTransform = new Avalonia.Media.ScaleTransform(1.0, 1.0);
                        await Task.Delay(100);
                    }

                    await UiAnimations.AddLoadedAsync(this.FindControl<TextBlock>("HeaderText"));
                    await UiAnimations.AddLoadedAsync(this.FindControl<TextBlock>("SubtitleText"), 100);
                    await UiAnimations.AddLoadedAsync(this.FindControl<Border>("MainContainer"), 200);

                    await AnimateFormElementsAsync();
                    await AnimateFooterElementsAsync();
                });
            }
            catch (Exception ex)
            {
                // Silently handle animation failures to prevent crashes
                System.Diagnostics.Debug.WriteLine($"Animation error: {ex.Message}");
            }
        }

        private async Task AnimateFormElementsAsync()
        {
            try
            {
                await UiAnimations.AddLoadedAsync(this.FindControl<TextBlock>("SectionHeader"), 120);

                var elements = new Control?[]
                {
                    this.FindControl<TextBlock>("HwidLabel"),
                    this.FindControl<TextBox>("HWIDTextBox"),
                    this.FindControl<TextBlock>("UsernameLabel"),
                    this.FindControl<TextBox>("UsernameTextBox"),
                    this.FindControl<TextBlock>("PasswordLabel"),
                    this.FindControl<TextBox>("PasswordTextBox"),
                    this.FindControl<CheckBox>("RememberMeCheckBox"),
                    this.FindControl<Button>("LoginButton")
                };

                await UiAnimations.AddLoadedSequentiallyAsync(elements, 90);
            }
            catch (Exception ex)
            {
                // Silently handle animation failures
                System.Diagnostics.Debug.WriteLine($"Form animation error: {ex.Message}");
            }
        }

        private async Task AnimateFooterElementsAsync()
        {
            try
            {
                await UiAnimations.AddLoadedAsync(this.FindControl<TextBlock>("SupportText"), 150);
                await UiAnimations.AddLoadedAsync(this.FindControl<Button>("WebsiteButton"));
            }
            catch (Exception ex)
            {
                // Silently handle animation failures
                System.Diagnostics.Debug.WriteLine($"Footer animation error: {ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                this.Opened -= OnWindowOpened;
            }
            catch { }

            Dispose();
            base.OnClosed(e);
        }

        private void OnShowPasswordClicked(object? sender, RoutedEventArgs e)
        {
            if (_isUpdating) return;
            _isPasswordVisible = !_isPasswordVisible;
            var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");
            if (passwordTextBox != null)
                passwordTextBox.PasswordChar = _isPasswordVisible ? '\0' : '•';
        }

        private async Task LoadHWIDAsync()
        {
            try
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    var hwidTextBox = this.FindControl<TextBox>("HWIDTextBox");
                    if (hwidTextBox != null) 
                        hwidTextBox.Text = "Generating...";
                });

                string hwid = await HWIDGenerator.GenerateHWIDAsync().ConfigureAwait(false);
                _currentHWID = hwid.ToUpperInvariant();

                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    var hwidTextBox = this.FindControl<TextBox>("HWIDTextBox");
                    if (hwidTextBox != null) 
                        hwidTextBox.Text = _currentHWID;
                });
            }
            catch
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    var hwidTextBox = this.FindControl<TextBox>("HWIDTextBox");
                    if (hwidTextBox != null) 
                        hwidTextBox.Text = "Error generating HWID";
                });
            }
        }

        private async void OnCopyHWIDClicked(object? sender, RoutedEventArgs e)
        {
            if (_isUpdating) return;
            try
            {
                var hwidTextBox = this.FindControl<TextBox>("HWIDTextBox");
                if (hwidTextBox?.Text != null && 
                    !string.IsNullOrEmpty(hwidTextBox.Text) &&
                    hwidTextBox.Text != "Generating..." && 
                    hwidTextBox.Text != "Error generating HWID")
                {
                    var clipboard = TopLevel.GetTopLevel(this)?.Clipboard;
                    if (clipboard != null)
                    {
                        await clipboard.SetTextAsync(hwidTextBox.Text);

                        var copyButton = this.FindControl<Button>("CopyHWIDButton");
                        if (copyButton != null)
                        {
                            string originalText = copyButton.Content?.ToString() ?? "COPY";
                            await Dispatcher.UIThread.InvokeAsync(() => copyButton.Content = "COPIED!");
                            await Task.Delay(1500);
                            await Dispatcher.UIThread.InvokeAsync(() => copyButton.Content = originalText);
                        }
                    }
                }
            }
            catch
            {
                await ShowMessageAsync("Failed to copy HWID to clipboard", "Copy Error").ConfigureAwait(false);
            }
        }

        private void LoadCredentials()
        {
            try
            {
                if (File.Exists(CredentialsFile))
                {
                    string data = File.ReadAllText(CredentialsFile);
                    if (!string.IsNullOrEmpty(data))
                    {
                        string[] parts = data.Split('|');
                        if (parts.Length == 3)
                        {
                            bool remember = parts[0] == "1";
                            if (remember)
                            {
                                string username = XorDecrypt(parts[1]);
                                string password = XorDecrypt(parts[2]);

                                SavedUsername = username;
                                
                                var usernameTextBox = this.FindControl<TextBox>("UsernameTextBox");
                                var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");
                                var rememberCheckBox = this.FindControl<CheckBox>("RememberMeCheckBox");

                                if (usernameTextBox != null)
                                    usernameTextBox.Text = username;
                                
                                if (passwordTextBox != null)
                                    passwordTextBox.Text = password;
                                
                                if (rememberCheckBox != null)
                                    rememberCheckBox.IsChecked = true;
                            }
                        }
                    }
                }
            }
            catch
            {
                // Silently handle credential loading failures
            }
        }

        private void SaveCredentials(bool remember, string username, string password)
        {
            try
            {
                if (!Directory.Exists(AppDataPath))
                    Directory.CreateDirectory(AppDataPath);

                if (remember && !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    string data = "1|" + XorEncrypt(username) + "|" + XorEncrypt(password);
                    File.WriteAllText(CredentialsFile, data);
                }
                else
                {
                    if (File.Exists(CredentialsFile))
                        File.Delete(CredentialsFile);
                }
            }
            catch
            {
                // Silently handle credential saving failures
            }
        }

        private static string XorEncrypt(string text)
        {
            if (string.IsNullOrEmpty(text)) return "";
            byte[] bytes = Encoding.UTF8.GetBytes(text);
            for (int i = 0; i < bytes.Length; i++)
                bytes[i] = (byte)(bytes[i] ^ 0x5A);
            return Convert.ToBase64String(bytes);
        }

        private static string XorDecrypt(string encoded)
        {
            if (string.IsNullOrEmpty(encoded)) return "";
            try
            {
                byte[] bytes = Convert.FromBase64String(encoded);
                for (int i = 0; i < bytes.Length; i++)
                    bytes[i] = (byte)(bytes[i] ^ 0x5A);
                return Encoding.UTF8.GetString(bytes);
            }
            catch 
            { 
                return ""; 
            }
        }

        private async Task SetLoggingInStateAsync(bool isLoggingIn)
        {
            _isLoggingIn = isLoggingIn;

            await Dispatcher.UIThread.InvokeAsync(() =>
            {
                var loginButton = this.FindControl<Button>("LoginButton");
                var usernameTextBox = this.FindControl<TextBox>("UsernameTextBox");
                var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");
                var rememberCheckBox = this.FindControl<CheckBox>("RememberMeCheckBox");
                var statusLabel = this.FindControl<TextBlock>("StatusLabel");

                if (loginButton != null)
                {
                    loginButton.IsEnabled = !isLoggingIn && !_isUpdating;
                    loginButton.Content = isLoggingIn ? "LOGGING IN..." : "LOGIN";
                }

                if (usernameTextBox != null)
                    usernameTextBox.IsEnabled = !isLoggingIn && !_isUpdating;

                if (passwordTextBox != null)
                    passwordTextBox.IsEnabled = !isLoggingIn && !_isUpdating;

                if (rememberCheckBox != null)
                    rememberCheckBox.IsEnabled = !isLoggingIn && !_isUpdating;

                if (statusLabel != null && !_isUpdating)
                    statusLabel.IsVisible = isLoggingIn;
            });
        }

        private async void OnLoginClicked(object? sender, RoutedEventArgs e)
        {
            if (_isLoggingIn || _isUpdating) return;

            if (UpdateRequired)
            {
                await ShowMessageAsync("Please wait for the update to complete.", "Update in Progress").ConfigureAwait(false);
                return;
            }

            var usernameTextBox = this.FindControl<TextBox>("UsernameTextBox");
            var passwordTextBox = this.FindControl<TextBox>("PasswordTextBox");

            string username = usernameTextBox?.Text ?? "";
            string password = passwordTextBox?.Text ?? "";

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                await ShowMessageAsync("Please enter both username and password.", "Login Failed").ConfigureAwait(false);
                return;
            }

            await SetLoggingInStateAsync(true).ConfigureAwait(false);

            try
            {
                string hwid = string.IsNullOrEmpty(_currentHWID) ?
                    await HWIDGenerator.GenerateHWIDAsync().ConfigureAwait(false) : _currentHWID;
                hwid = hwid.ToUpperInvariant();

                var requestObj = new JsonObject
                {
                    ["username"] = username,
                    ["password"] = password,
                    ["Hardware"] = hwid
                };

                string jsonData = requestObj.ToJsonString();
                string encryptedData = _encryption.Encrypt(jsonData);

                var requestPayload = new JsonObject
                {
                    ["data"] = encryptedData
                };

                using var content = new StringContent(requestPayload.ToJsonString(), Encoding.UTF8, "application/json");
                using var response = await _httpClient.PostAsync("https://samsungtool.service-app.org/api/user/login", content).ConfigureAwait(false);
                string responseJson = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        var responseObj = JsonObject.Parse(responseJson);

                        if (responseObj?["data"]?.GetValue<string>() is { } dataValue && !string.IsNullOrEmpty(dataValue))
                        {
                            string decryptedResponse = _encryption.Decrypt(dataValue);
                            var responseData = JsonObject.Parse(decryptedResponse);
                            
                            if (responseData?["Status"]?.GetValue<bool>() is bool status &&
                                responseData["Message"]?.GetValue<string>() is string message)
                            {
                                if (!status)
                                {
                                    await ShowMessageAsync(message, "Login Failed").ConfigureAwait(false);
                                    return;
                                }

                                if (responseData["Token"]?.GetValue<string>() is string tokenValue && 
                                    !string.IsNullOrEmpty(tokenValue))
                                {
                                    AuthToken = tokenValue;
                                    TokenExpiresAt = DateTime.UtcNow.AddHours(2);
                                    SavedUsername = username;

                                    var rememberChecked = false;
                                    await Dispatcher.UIThread.InvokeAsync(() =>
                                    {
                                        var chk = this.FindControl<CheckBox>("RememberMeCheckBox");
                                        rememberChecked = chk?.IsChecked ?? false;
                                    });
                                    SaveCredentials(rememberChecked, username, password);
                                    await Dispatcher.UIThread.InvokeAsync(() => Close());
                                    return;
                                }

                                await ShowMessageAsync("Server returned an invalid token", "Login Failed").ConfigureAwait(false);
                            }
                        }
                        else
                        {
                            await ShowMessageAsync("Server returned an invalid response", "Login Failed").ConfigureAwait(false);
                        }
                    }
                    catch
                    {
                        await ShowMessageAsync("Error processing server response", "Login Error").ConfigureAwait(false);
                    }
                }
                else
                {
                    try
                    {
                        var responseObj = JsonObject.Parse(responseJson);
                        if (responseObj?["data"]?.GetValue<string>() is { } errorData && !string.IsNullOrEmpty(errorData))
                        {
                            try
                            {
                                string decryptedError = _encryption.Decrypt(errorData);
                                var errorDataObj = JsonObject.Parse(decryptedError);
                                if (errorDataObj?["Message"]?.GetValue<string>() is string errorMessage && 
                                    !string.IsNullOrEmpty(errorMessage))
                                {
                                    await ShowMessageAsync(errorMessage, "Login Failed").ConfigureAwait(false);
                                    return;
                                }
                            }
                            catch 
                            {
                                // Fall through to generic error
                            }
                        }
                    }
                    catch
                    {
                        await ShowMessageAsync($"Server error: {response.StatusCode}", "Login Failed").ConfigureAwait(false);
                    }
                }
            }
            catch
            {
                await ShowMessageAsync("Connection error occurred", "Login Error").ConfigureAwait(false);
            }
            finally
            {
                await SetLoggingInStateAsync(false).ConfigureAwait(false);
            }
        }

        private async Task ShowMessageAsync(string message, string title)
        {
            await Views.UiDialogs.ShowOkAsync(this, title, message, topmost: true, undecoratedAndLocked: true);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                try
                {
                    _securityChecker?.Dispose();
                    _securityChecker = null;
                }
                catch 
                {
                    // Silently handle disposal failures
                }

                try
                {
                    _encryption.Dispose();
                }
                catch 
                {
                    // Silently handle disposal failures
                }
            }
            _disposed = true;
        }

        ~LoginWindow()
        {
            Dispose(false);
        }
    }

    public sealed class UpdateInfo
    {
        public Version Version { get; set; } = new Version(1, 0, 0, 0);
        public string DownloadUrl { get; set; } = "";
        public string Changelog { get; set; } = "";
        public bool IsMandatory { get; set; }
    }
}