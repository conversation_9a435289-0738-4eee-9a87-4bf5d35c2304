using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Layout;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using QRCoder;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace SamsungTool
{
    public class AdminExtrasBundle
    {
        [JsonPropertyName("enrollmentId")]
        public string? EnrollmentId { get; set; }
    }

    public class AndroidProvisioningData
    {
        [JsonPropertyName("android.app.extra.PROVISIONING_ADMIN_EXTRAS_BUNDLE")]
        public AdminExtrasBundle? AdminExtrasBundle { get; set; }

        [JsonPropertyName("android.app.extra.PROVISIONING_DEVICE_ADMIN_COMPONENT_NAME")]
        public string? DeviceAdminComponentName { get; set; }

        [JsonPropertyName("android.app.extra.PROVISIONING_DEVICE_ADMIN_PACKAGE_CHECKSUM")]
        public string? DeviceAdminPackageChecksum { get; set; }

        [JsonPropertyName("android.app.extra.PROVISIONING_DEVICE_ADMIN_PACKAGE_DOWNLOAD_LOCATION")]
        public string? DeviceAdminPackageDownloadLocation { get; set; }

        [JsonPropertyName("android.app.extra.PROVISIONING_LEAVE_ALL_SYSTEM_APPS_ENABLED")]
        public bool LeaveAllSystemAppsEnabled { get; set; }

        [JsonPropertyName("android.app.extra.PROVISIONING_SKIP_ENCRYPTION")]
        public bool SkipEncryption { get; set; }
    }

    public partial class QRWindow : Window, IDisposable
    {
        private bool _disposed = false;
        private int _osVersion;
        private Image? _qrImage;

        public QRWindow()
        {
            InitializeComponent();
        }

        public QRWindow(int os, string sn)
        {
            _osVersion = os;

            Console.WriteLine($"OS {os}");
            if (os == 13 || os == 14)
            {
                InitializeComponent();
                _ = Task.Run(async () => await BuildAndShowQrAsync(sn));
            }
            else
            {
                this.Close();
            }
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);

            // Find the QR image element by name
            _qrImage = this.FindControl<Image>("QrImage");
        }

        private async Task BuildAndShowQrAsync(string serialNumber)
        {
            try
            {
                var encryptedSerial = await EncryptSerialNumber(serialNumber);
                string json = BuildJsonPayload(encryptedSerial);
                var qrCodeBitmap = await CreateQrCodeBitmapAsync(json);

                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    if (!_disposed && _qrImage != null)
                    {
                        _qrImage.Source = qrCodeBitmap;
                    }
                });
            }
            catch (Exception ex)
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    if (!_disposed && Content is StackPanel panel)
                    {
                        var errorText = new TextBlock
                        {
                            Text = $"Failed to generate QR code: {ex.Message}",
                            HorizontalAlignment = HorizontalAlignment.Center,
                            Margin = new Thickness(0.0, 10.0, 0.0, 0.0),
                            FontSize = 12,
                            Foreground = new SolidColorBrush(Color.Parse("#FF6B6B")),
                            FontStyle = FontStyle.Italic
                        };

                        if (panel.Children.Count == 4)
                        {
                            panel.Children.Insert(3, errorText);
                        }
                    }
                });
            }
        }

        private string BuildJsonPayload(string enrollId)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append('{');

            sb.Append("\"android.app.extra.PROVISIONING_ADMIN_EXTRAS_BUNDLE\":{");
            sb.Append($"\"enrollmentId\":\"{enrollId}\"");
            sb.Append("},");

            sb.Append("\"android.app.extra.PROVISIONING_DEVICE_ADMIN_COMPONENT_NAME\":");
            sb.Append("\"com.mdm.guard.knox/com.mdm.guard.knox.Hubris\",");

            sb.Append("\"android.app.extra.PROVISIONING_DEVICE_ADMIN_PACKAGE_CHECKSUM\":");
            sb.Append("\"_9Jqq8QDFFTKWCCtY8HhQ6lW6BjGrQcWtq9TgSIbV6o=\",");

            sb.Append("\"android.app.extra.PROVISIONING_DEVICE_ADMIN_PACKAGE_DOWNLOAD_LOCATION\":");
            sb.Append("\"https://samsungtool.service-app.org/adb.apk\",");

            sb.Append("\"android.app.extra.PROVISIONING_LEAVE_ALL_SYSTEM_APPS_ENABLED\":");
            sb.Append("true,");

            sb.Append("\"android.app.extra.PROVISIONING_SKIP_ENCRYPTION\":");
            sb.Append("true");

            sb.Append('}');
            return sb.ToString();
        }

        private async Task<Bitmap> CreateQrCodeBitmapAsync(string data)
        {
            return await Task.Run(() =>
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.M);
                using var qrCode = new PngByteQRCode(qrCodeData);

                var qrCodeBytes = qrCode.GetGraphic(80, new byte[] { 0, 0, 0 }, new byte[] { 255, 255, 255 });

                using var stream = new MemoryStream(qrCodeBytes);
                return new Bitmap(stream);
            });
        }

        public async Task<string> EncryptSerialNumber(string serialNumber)
        {
            return await Task.Run(() =>
            {
                if (string.IsNullOrEmpty(serialNumber))
                    return Convert.ToBase64String(new byte[32]).Replace('+', '-').Replace('/', '_');

                using var sha256 = SHA256.Create();
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(serialNumber));
                return Convert.ToBase64String(hashBytes).Replace('+', '-').Replace('/', '_');
            });
        }

        private void CloseButton_Click(object? sender, RoutedEventArgs e)
        {
            this.Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            Dispose();
            base.OnClosed(e);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                try
                {
                    if (_qrImage?.Source is IDisposable disposableImage)
                    {
                        disposableImage.Dispose();
                    }
                }
                catch { }
            }

            _disposed = true;
        }

        ~QRWindow()
        {
            Dispose(false);
        }
    }
}