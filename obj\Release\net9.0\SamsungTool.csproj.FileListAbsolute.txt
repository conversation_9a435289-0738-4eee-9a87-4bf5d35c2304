D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SamsungTool.exe
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SamsungTool.deps.json
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SamsungTool.runtimeconfig.json
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SamsungTool.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SamsungTool.pdb
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Base.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Controls.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.DesignerSupport.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Dialogs.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Markup.Xaml.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Markup.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Metal.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.MicroCom.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.OpenGL.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Vulkan.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Desktop.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Fonts.Inter.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.FreeDesktop.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Native.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Remote.Protocol.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Skia.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Themes.Fluent.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Win32.Automation.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.Win32.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Avalonia.X11.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\CommunityToolkit.Mvvm.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\HarfBuzzSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\K4os.Compression.LZ4.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\K4os.Compression.LZ4.Streams.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\K4os.Hash.xxHash.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\MicroCom.Runtime.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.DependencyInjection.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.Logging.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.Options.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Extensions.Primitives.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Microsoft.Win32.SystemEvents.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\QRCoder.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\SkiaSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\System.Diagnostics.EventLog.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\System.Drawing.Common.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\System.Private.Windows.Core.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\System.IO.Ports.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\System.ServiceProcess.ServiceController.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\Tmds.DBus.Protocol.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-arm64\native\av_libglesv2.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x64\native\av_libglesv2.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x86\native\av_libglesv2.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\osx\native\libAvaloniaNative.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm\native\libHarfBuzzSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm64\native\libHarfBuzzSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-x64\native\libHarfBuzzSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\osx\native\libHarfBuzzSharp.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-arm64\native\libHarfBuzzSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x64\native\libHarfBuzzSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x86\native\libHarfBuzzSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win\lib\net9.0\Microsoft.Win32.SystemEvents.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm\native\libSkiaSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-arm64\native\libSkiaSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\linux-x64\native\libSkiaSharp.so
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\osx\native\libSkiaSharp.dylib
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-arm64\native\libSkiaSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x64\native\libSkiaSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win-x86\native\libSkiaSharp.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.Messages.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\unix\lib\net9.0\System.IO.Ports.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win\lib\net9.0\System.IO.Ports.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Release\net9.0\runtimes\win\lib\net9.0\System.ServiceProcess.ServiceController.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.csproj.AssemblyReference.cache
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\Avalonia\Resources.Inputs.cache
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\Avalonia\resources
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.GeneratedMSBuildEditorConfig.editorconfig
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.AssemblyInfoInputs.cache
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.AssemblyInfo.cs
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.csproj.CoreCompileInputs.cache
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungT.9D4B3B5B.Up2Date
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\refint\SamsungTool.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.pdb
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\SamsungTool.genruntimeconfig.cache
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Release\net9.0\ref\SamsungTool.dll
