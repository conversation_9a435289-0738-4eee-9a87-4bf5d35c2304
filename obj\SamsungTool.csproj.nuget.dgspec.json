{"format": 1, "restore": {"D:\\Telegram_Download\\SamsungTool_refactored_20250604173140 (3)2\\SamsungTool.csproj": {}}, "projects": {"D:\\Telegram_Download\\SamsungTool_refactored_20250604173140 (3)2\\SamsungTool.csproj": {"version": "2.0.5.4", "restore": {"projectUniqueName": "D:\\Telegram_Download\\SamsungTool_refactored_20250604173140 (3)2\\SamsungTool.csproj", "projectName": "SamsungTool", "projectPath": "D:\\Telegram_Download\\SamsungTool_refactored_20250604173140 (3)2\\SamsungTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Telegram_Download\\SamsungTool_refactored_20250604173140 (3)2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "K4os.Compression.LZ4.Streams": {"target": "Package", "version": "[1.3.8, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[9.0.8, )", "autoReferenced": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.8, )", "autoReferenced": true}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}, "System.IO.Compression": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Compression.ZipFile": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.5, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[9.0.5, )"}, "runtime.native.System.IO.Ports": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[9.0.8, 9.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}